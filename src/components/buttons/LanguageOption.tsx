import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Pressable,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { SvgProps } from 'react-native-svg';
import { Checkmark } from '~/components/icons';
import colors from '~/styles/colors';
import { h3, grayishText } from '~/styles/text';
import { shadowSmall, cardView } from '~/styles/views';
import { marginLeft12 } from '~/styles/spacing';

interface LanguageOptionProps {
  languageName: string;
  secondaryText?: string;
  FlagComponent: React.FC<SvgProps>;
  isSelected?: boolean;
  onPress?: () => void;
}

const LanguageOption = ({
  languageName,
  secondaryText,
  FlagComponent,
  isSelected = false,
  onPress,
}: LanguageOptionProps) => {
  return (
    <Pressable onPress={onPress} style={cardView} testID="language-option">
      <FlagComponent style={styles.flag} />
      <View style={styles.textContainer}>
        <Text style={styles.languageName}>{languageName}</Text>
        {secondaryText && (
          <Text style={styles.secondaryText}>{secondaryText}</Text>
        )}
      </View>
      <View style={marginLeft12}>
        {isSelected && <Checkmark color={colors.greenDark} size={32} />}
      </View>
    </Pressable>
  );
};

const styles = StyleSheet.create({
  flag: {
    width: 33,
    height: 20,
    borderRadius: 2,
    marginRight: 16,
  },
  textContainer: {
    flex: 1,
    rowGap: 4,
  },
  languageName: {
    ...h3,
    fontSize: 16,
    color: colors.grey900,
  } as TextStyle,
  secondaryText: {
    ...grayishText,
    fontSize: 14,
    color: colors.grey600,
  } as TextStyle,
});

export default LanguageOption;
